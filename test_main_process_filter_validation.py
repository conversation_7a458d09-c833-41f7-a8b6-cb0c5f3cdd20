"""
任务4.1：主进程过滤集成验证脚本
验证规则预过滤功能在统一校验流程中的正确集成

验证内容：
1. 代码语法正确性
2. 导入依赖可用性
3. 配置项正确性
4. 集成逻辑完整性
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入功能"""
    print("🔍 测试基本导入功能...")
    
    try:
        # 测试核心组件导入
        from core.rule_prefilter import rule_prefilter
        from core.patient_data_analyzer import patient_data_analyzer
        from core.rule_index_manager import FilterResult
        
        print("✅ 核心组件导入成功")
        
        # 验证实例存在
        assert rule_prefilter is not None, "rule_prefilter实例不存在"
        assert patient_data_analyzer is not None, "patient_data_analyzer实例不存在"
        assert hasattr(rule_prefilter, 'filter_rules_for_patient'), "缺少filter_rules_for_patient方法"
        
        print("✅ 实例和方法检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本导入测试失败: {e}")
        return False

def test_configuration():
    """测试配置项"""
    print("🔍 测试配置项...")
    
    try:
        from config.settings import settings
        
        # 检查预过滤相关配置
        config_items = [
            'ENABLE_RULE_PREFILTER',
            'PREFILTER_TIMEOUT_MS',
            'PREFILTER_FALLBACK_THRESHOLD',
            'ENABLE_ULTRA_FAST_VALIDATION'
        ]
        
        for item in config_items:
            value = getattr(settings, item, 'NOT_SET')
            print(f"✅ 配置检查: {item} = {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_validation_logic_syntax():
    """测试validation_logic.py语法正确性"""
    print("🔍 测试validation_logic.py语法...")
    
    try:
        # 读取文件内容并检查语法
        with open('api/routers/common/validation_logic.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 编译检查语法
        compile(content, 'api/routers/common/validation_logic.py', 'exec')
        print("✅ validation_logic.py语法检查通过")
        
        # 检查关键集成代码是否存在
        key_patterns = [
            'ENABLE_RULE_PREFILTER',
            'rule_prefilter.filter_rules_for_patient',
            'filter_result.filtered_rule_ids',
            'rule_prefilter_applied',
            'rule_prefilter_fallback'
        ]
        
        for pattern in key_patterns:
            if pattern in content:
                print(f"✅ 找到关键代码: {pattern}")
            else:
                print(f"❌ 缺少关键代码: {pattern}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def test_integration_logic():
    """测试集成逻辑完整性"""
    print("🔍 测试集成逻辑完整性...")
    
    try:
        # 读取validation_logic.py内容
        with open('api/routers/common/validation_logic.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查集成逻辑的完整性
        integration_checks = [
            # 检查预过滤启用检查
            ('预过滤启用检查', 'if settings.ENABLE_RULE_PREFILTER:'),
            
            # 检查预过滤器导入
            ('预过滤器导入', 'from core.rule_prefilter import rule_prefilter'),
            
            # 检查过滤方法调用
            ('过滤方法调用', 'rule_prefilter.filter_rules_for_patient'),
            
            # 检查规则ID更新
            ('规则ID更新', 'request.ids = filter_result.filtered_rule_ids'),
            
            # 检查异常处理
            ('异常处理', 'except Exception as e:'),
            
            # 检查降级日志
            ('降级日志', '规则预过滤失败，降级到全量校验'),
            
            # 检查请求跟踪
            ('请求跟踪', 'rule_prefilter_applied'),
            
            # 检查降级跟踪
            ('降级跟踪', 'rule_prefilter_fallback'),
            
            # 检查性能统计
            ('性能统计', 'get_performance_stats'),
        ]
        
        all_passed = True
        for check_name, pattern in integration_checks:
            if pattern in content:
                print(f"✅ {check_name}: 找到")
            else:
                print(f"❌ {check_name}: 缺失 - {pattern}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 集成逻辑检查失败: {e}")
        return False

def test_filter_result_structure():
    """测试FilterResult数据结构"""
    print("🔍 测试FilterResult数据结构...")
    
    try:
        from core.rule_index_manager import FilterResult
        
        # 创建测试FilterResult实例
        test_result = FilterResult(
            original_rule_count=10,
            filtered_rule_count=4,
            filter_rate=0.6,
            filter_time=3.5,
            filtered_rule_ids=["rule1", "rule2", "rule3", "rule4"],
            fallback_reason=None
        )
        
        # 验证属性
        assert test_result.original_rule_count == 10
        assert test_result.filtered_rule_count == 4
        assert test_result.filter_rate == 0.6
        assert test_result.filter_time == 3.5
        assert len(test_result.filtered_rule_ids) == 4
        
        print("✅ FilterResult结构验证通过")
        return True
        
    except Exception as e:
        print(f"❌ FilterResult测试失败: {e}")
        return False

def test_performance_monitoring():
    """测试性能监控功能"""
    print("🔍 测试性能监控功能...")
    
    try:
        from core.rule_prefilter import rule_prefilter
        
        # 检查性能统计方法
        assert hasattr(rule_prefilter, 'get_performance_stats'), "缺少get_performance_stats方法"
        assert hasattr(rule_prefilter, 'reset_stats'), "缺少reset_stats方法"
        assert hasattr(rule_prefilter, 'is_healthy'), "缺少is_healthy方法"
        
        # 获取性能统计
        stats = rule_prefilter.get_performance_stats()
        assert isinstance(stats, dict), "性能统计应该返回字典"
        
        expected_keys = ['filter_count', 'avg_filter_time_ms', 'avg_filter_rate', 'fallback_count']
        for key in expected_keys:
            assert key in stats, f"性能统计缺少{key}字段"
        
        print("✅ 性能监控功能验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 性能监控测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始任务4.1：主进程过滤集成验证")
    print("=" * 60)
    
    tests = [
        ("基本导入功能", test_basic_imports),
        ("配置项检查", test_configuration),
        ("语法正确性", test_validation_logic_syntax),
        ("集成逻辑完整性", test_integration_logic),
        ("FilterResult结构", test_filter_result_structure),
        ("性能监控功能", test_performance_monitoring),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！主进程过滤集成验证成功")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
